<template>
  <div class="pwa-install-prompt" v-if="showPrompt">
    <!-- 浮动安装提示 -->
    <transition name="slide-up">
      <div class="install-banner" v-if="showBanner && !isInstalled">
        <div class="banner-content">
          <div class="banner-icon">
            <el-icon><Download /></el-icon>
          </div>
          <div class="banner-text">
            <div class="banner-title">安装AI学习助手</div>
            <div class="banner-desc">添加到主屏幕，获得更好的使用体验</div>
          </div>
          <div class="banner-actions">
            <el-button type="primary" size="small" @click="handleInstall">
              安装
            </el-button>
            <el-button size="small" @click="dismissBanner">
              稍后
            </el-button>
          </div>
        </div>
        <el-button
          class="close-btn"
          link
          size="small"
          @click="closeBanner"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </transition>

    <!-- 详细安装对话框 -->
    <el-dialog
      v-model="showDialog"
      title="安装AI学习助手"
      width="500px"
      :before-close="handleDialogClose"
    >
      <div class="install-dialog-content">
        <div class="app-preview">
          <img src="/icon-192x192.png" alt="AI学习助手" class="app-icon">
          <div class="app-info">
            <h3>AI学习助手</h3>
            <p>AI驱动的个性化学习与求职辅导平台</p>
          </div>
        </div>

        <div class="install-benefits">
          <h4>安装后您将获得：</h4>
          <ul>
            <li>
              <el-icon><Check /></el-icon>
              <span>更快的启动速度</span>
            </li>
            <li>
              <el-icon><Check /></el-icon>
              <span>离线学习功能</span>
            </li>
            <li>
              <el-icon><Check /></el-icon>
              <span>推送通知提醒</span>
            </li>
            <li>
              <el-icon><Check /></el-icon>
              <span>原生应用体验</span>
            </li>
            <li>
              <el-icon><Check /></el-icon>
              <span>节省手机存储空间</span>
            </li>
          </ul>
        </div>

        <div class="install-steps" v-if="!canInstall">
          <h4>手动安装步骤：</h4>
          <div class="steps-content">
            <div class="step" v-for="(step, index) in installSteps" :key="index">
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-text">{{ step }}</div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleInstall"
            :disabled="!canInstall"
          >
            {{ canInstall ? '立即安装' : '了解更多' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 安装成功提示 -->
    <el-dialog
      v-model="showSuccessDialog"
      title="安装成功"
      width="400px"
      :show-close="false"
    >
      <div class="success-content">
        <el-icon class="success-icon"><SuccessFilled /></el-icon>
        <h3>安装成功！</h3>
        <p>AI学习助手已添加到您的主屏幕</p>
        <p>您现在可以像使用原生应用一样使用它了</p>
      </div>
      
      <template #footer>
        <el-button type="primary" @click="showSuccessDialog = false">
          开始使用
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Close, Check, SuccessFilled } from '@element-plus/icons-vue'

// 响应式数据
const showPrompt = ref(false)
const showBanner = ref(false)
const showDialog = ref(false)
const showSuccessDialog = ref(false)
const isInstalled = ref(false)
const canInstall = ref(false)
const deferredPrompt = ref<any>(null)

// 计算属性
const installSteps = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
    return [
      '点击浏览器右上角的菜单按钮（三个点）',
      '选择"安装AI学习助手"或"添加到主屏幕"',
      '在弹出的对话框中点击"安装"',
      '应用将添加到您的桌面或应用列表中'
    ]
  } else if (userAgent.includes('safari') && userAgent.includes('mobile')) {
    return [
      '点击浏览器底部的分享按钮',
      '向下滚动找到"添加到主屏幕"选项',
      '点击"添加到主屏幕"',
      '在弹出的对话框中点击"添加"'
    ]
  } else if (userAgent.includes('firefox')) {
    return [
      '点击地址栏右侧的安装图标',
      '在弹出的提示中点击"安装"',
      '应用将添加到您的桌面或应用列表中'
    ]
  } else {
    return [
      '在浏览器菜单中查找"安装"或"添加到主屏幕"选项',
      '按照浏览器提示完成安装',
      '应用将添加到您的设备中'
    ]
  }
})

// 生命周期
onMounted(() => {
  checkInstallability()
  setupEventListeners()
  checkIfShouldShowPrompt()
})

onUnmounted(() => {
  removeEventListeners()
})

// 检查安装能力
const checkInstallability = () => {
  // 检查是否已安装
  if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
    isInstalled.value = true
    return
  }

  // 检查是否支持安装
  if ('serviceWorker' in navigator && 'BeforeInstallPromptEvent' in window) {
    canInstall.value = true
  }
}

// 设置事件监听器
const setupEventListeners = () => {
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  window.addEventListener('appinstalled', handleAppInstalled)
}

// 移除事件监听器
const removeEventListeners = () => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  window.removeEventListener('appinstalled', handleAppInstalled)
}

// 处理安装提示事件
const handleBeforeInstallPrompt = (e: Event) => {
  e.preventDefault()
  deferredPrompt.value = e
  canInstall.value = true
  
  // 延迟显示安装提示
  setTimeout(() => {
    if (!isInstalled.value && shouldShowPrompt()) {
      showPrompt.value = true
      showBanner.value = true
    }
  }, 3000) // 3秒后显示
}

// 处理应用安装事件
const handleAppInstalled = () => {
  isInstalled.value = true
  showPrompt.value = false
  showBanner.value = false
  showDialog.value = false
  showSuccessDialog.value = true
  
  // 记录安装状态
  localStorage.setItem('pwa-installed', 'true')
  
  ElMessage.success('应用安装成功！')
}

// 检查是否应该显示提示
const shouldShowPrompt = (): boolean => {
  const dismissed = localStorage.getItem('pwa-install-dismissed')
  const installed = localStorage.getItem('pwa-installed')
  const lastShown = localStorage.getItem('pwa-install-last-shown')
  
  if (installed === 'true' || dismissed === 'true') {
    return false
  }
  
  // 如果最近显示过，等待一段时间再显示
  if (lastShown) {
    const lastShownTime = new Date(lastShown).getTime()
    const now = new Date().getTime()
    const daysSinceLastShown = (now - lastShownTime) / (1000 * 60 * 60 * 24)
    
    return daysSinceLastShown >= 7 // 7天后再次显示
  }
  
  return true
}

// 检查是否应该显示提示
const checkIfShouldShowPrompt = () => {
  if (isInstalled.value) {
    return
  }
  
  // 检查用户访问次数
  const visitCount = parseInt(localStorage.getItem('visit-count') || '0') + 1
  localStorage.setItem('visit-count', visitCount.toString())
  
  // 访问3次后显示提示
  if (visitCount >= 3 && shouldShowPrompt()) {
    setTimeout(() => {
      showPrompt.value = true
      showBanner.value = true
    }, 5000) // 5秒后显示
  }
}

// 处理安装
const handleInstall = async () => {
  if (!deferredPrompt.value) {
    showDialog.value = true
    return
  }
  
  try {
    // 显示安装提示
    deferredPrompt.value.prompt()
    
    // 等待用户响应
    const { outcome } = await deferredPrompt.value.userChoice
    
    if (outcome === 'accepted') {
      console.log('User accepted the install prompt')
    } else {
      console.log('User dismissed the install prompt')
    }
    
    // 清理
    deferredPrompt.value = null
    showBanner.value = false
    showDialog.value = false
    
  } catch (error) {
    console.error('Install failed:', error)
    ElMessage.error('安装失败，请稍后重试')
  }
}

// 暂时关闭横幅
const dismissBanner = () => {
  showBanner.value = false
  localStorage.setItem('pwa-install-last-shown', new Date().toISOString())
}

// 永久关闭横幅
const closeBanner = () => {
  showBanner.value = false
  showPrompt.value = false
  localStorage.setItem('pwa-install-dismissed', 'true')
}

// 处理对话框关闭
const handleDialogClose = () => {
  showDialog.value = false
}

// 暴露方法给父组件
defineExpose({
  showInstallDialog: () => {
    showDialog.value = true
  },
  checkInstallability
})
</script>

<style scoped>
.pwa-install-prompt {
  position: relative;
}

.install-banner {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  max-width: 500px;
  margin: 0 auto;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.banner-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 12px;
}

.banner-icon {
  font-size: 24px;
  opacity: 0.9;
}

.banner-text {
  flex: 1;
}

.banner-title {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.banner-desc {
  font-size: 14px;
  opacity: 0.9;
}

.banner-actions {
  display: flex;
  gap: 8px;
}

.close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  color: white;
  opacity: 0.7;
}

.close-btn:hover {
  opacity: 1;
}

.install-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.app-preview {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #F8F9FA;
  border-radius: 12px;
}

.app-icon {
  width: 64px;
  height: 64px;
  border-radius: 12px;
}

.app-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.app-info p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.install-benefits h4,
.install-steps h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.install-benefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.install-benefits li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  color: #606266;
}

.install-benefits li .el-icon {
  color: #67C23A;
  font-size: 16px;
}

.steps-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.step {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.step-number {
  width: 24px;
  height: 24px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-text {
  color: #606266;
  line-height: 1.5;
}

.success-content {
  text-align: center;
  padding: 20px;
}

.success-icon {
  font-size: 48px;
  color: #67C23A;
  margin-bottom: 16px;
}

.success-content h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 20px;
}

.success-content p {
  margin: 8px 0;
  color: #606266;
  line-height: 1.5;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

@media (max-width: 768px) {
  .install-banner {
    left: 10px;
    right: 10px;
    bottom: 10px;
  }
  
  .banner-content {
    padding: 12px 16px;
  }
  
  .banner-actions {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
