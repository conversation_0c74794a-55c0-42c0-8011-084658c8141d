/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AppFooter: typeof import('./src/components/layout/AppFooter.vue')['default']
    AppHeader: typeof import('./src/components/layout/AppHeader.vue')['default']
    ArticleCard: typeof import('./src/components/favorite/ArticleCard.vue')['default']
    ArticleFavorites: typeof import('./src/components/favorite/ArticleFavorites.vue')['default']
    ArticleListItem: typeof import('./src/components/favorite/ArticleListItem.vue')['default']
    CommentItem: typeof import('./src/components/comment/CommentItem.vue')['default']
    CommentList: typeof import('./src/components/comment/CommentList.vue')['default']
    CourseCard: typeof import('./src/components/favorite/CourseCard.vue')['default']
    CourseDataTable: typeof import('./src/components/admin/CourseDataTable.vue')['default']
    CourseFavorites: typeof import('./src/components/favorite/CourseFavorites.vue')['default']
    CourseListItem: typeof import('./src/components/favorite/CourseListItem.vue')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElFooter: typeof import('element-plus/es')['ElFooter']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    FavoriteButton: typeof import('./src/components/interaction/FavoriteButton.vue')['default']
    FavoriteItem: typeof import('./src/components/favorite/FavoriteItem.vue')['default']
    InteractionToolbar: typeof import('./src/components/interaction/InteractionToolbar.vue')['default']
    LearningAnalytics: typeof import('./src/components/LearningAnalytics.vue')['default']
    LearningDataTable: typeof import('./src/components/admin/LearningDataTable.vue')['default']
    LikeButton: typeof import('./src/components/interaction/LikeButton.vue')['default']
    MonitoringDashboard: typeof import('./src/components/monitoring/MonitoringDashboard.vue')['default']
    PerformanceDataTable: typeof import('./src/components/admin/PerformanceDataTable.vue')['default']
    PerformanceMonitor: typeof import('./src/components/PerformanceMonitor.vue')['default']
    PushNotificationSettings: typeof import('./src/components/PushNotificationSettings.vue')['default']
    PWAInstallPrompt: typeof import('./src/components/PWAInstallPrompt.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SecurityForm: typeof import('./src/components/security/SecurityForm.vue')['default']
    SecurityMonitor: typeof import('./src/components/security/SecurityMonitor.vue')['default']
    SmartRecommendations: typeof import('./src/components/SmartRecommendations.vue')['default']
    UserDataTable: typeof import('./src/components/admin/UserDataTable.vue')['default']
    UserDetailDialog: typeof import('./src/components/admin/UserDetailDialog.vue')['default']
    UserEditDialog: typeof import('./src/components/admin/UserEditDialog.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
