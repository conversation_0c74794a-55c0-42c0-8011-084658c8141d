#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -x "$basedir//bin/bash" ]; then
  exec "$basedir//bin/bash"  "$basedir/../vega-lite/bin/vl2png" "$@"
else 
  exec /bin/bash  "$basedir/../vega-lite/bin/vl2png" "$@"
fi
