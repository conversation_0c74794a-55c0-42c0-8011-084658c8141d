<template>
  <div class="app-header">
    <div class="header-content">
      <!-- Logo和标题 -->
      <div class="logo-section">
        <router-link to="/" class="logo-link">
          <el-icon size="32" color="#409EFF">
            <Reading />
          </el-icon>
          <span class="logo-text">AI学习助手</span>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <div class="nav-section">
        <el-menu
          mode="horizontal"
          :default-active="activeIndex"
          class="nav-menu"
          @select="handleSelect"
        >
          <el-menu-item index="/">首页</el-menu-item>
          <el-menu-item index="/courses">课程</el-menu-item>
          <el-menu-item index="/articles">文章</el-menu-item>
          <el-sub-menu index="ai">
            <template #title>
              <el-icon><MagicStick /></el-icon>
              AI功能
            </template>
            <el-menu-item index="/ai-recommendations">智能推荐</el-menu-item>
            <el-menu-item index="/learning-analytics">学习分析</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div>

      <!-- 搜索框 -->
      <div class="search-section">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索课程、文章..."
          class="search-input"
          @keyup.enter="handleSearch"
        >
          <template #suffix>
            <el-icon class="search-icon" @click="handleSearch">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <!-- 用户操作区 -->
      <div class="user-section">
        <template v-if="authStore.isAuthenticated">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="authStore.user?.avatar">
                {{ authStore.user?.nickname?.charAt(0) }}
              </el-avatar>
              <span class="username">{{ authStore.user?.nickname }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                <el-dropdown-item command="learning-stats">学习统计</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
        <template v-else>
          <el-button link @click="$router.push('/login')">登录</el-button>
          <el-button type="primary" @click="$router.push('/register')">注册</el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import { MagicStick } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const searchKeyword = ref('')

const activeIndex = computed(() => route.path)

const handleSelect = (key: string) => {
  router.push(key)
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/search',
      query: { keyword: searchKeyword.value.trim() }
    })
  }
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'learning-stats':
      router.push('/learning-stats')
      break
    case 'logout':
      authStore.logout()
      ElMessage.success('退出登录成功')
      router.push('/')
      break
  }
}
</script>

<style scoped>
.app-header {
  height: 60px;
  border-bottom: 1px solid #e4e7ed;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.logo-section {
  flex-shrink: 0;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #303133;
}

.logo-text {
  margin-left: 8px;
  font-size: 20px;
  font-weight: bold;
  color: #409EFF;
}

.nav-section {
  flex: 1;
  margin: 0 40px;
}

.nav-menu {
  border-bottom: none;
}

.search-section {
  flex-shrink: 0;
  margin-right: 20px;
}

.search-input {
  width: 300px;
}

.search-icon {
  cursor: pointer;
}

.user-section {
  flex-shrink: 0;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  color: #303133;
}
</style>
