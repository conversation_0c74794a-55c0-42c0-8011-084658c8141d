/**
 * 安全工具集
 * 提供输入验证、XSS防护、CSRF保护等安全功能
 */

// XSS防护
export class XSSProtection {
  private static readonly HTML_ENTITIES: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '/': '&#x2F;',
    '`': '&#x60;',
    '=': '&#x3D;'
  }

  /**
   * HTML实体编码
   */
  static escapeHtml(text: string): string {
    return text.replace(/[&<>"'`=\/]/g, (match) => {
      return XSSProtection.HTML_ENTITIES[match] || match
    })
  }

  /**
   * HTML实体解码
   */
  static unescapeHtml(text: string): string {
    const entityMap = Object.fromEntries(
      Object.entries(XSSProtection.HTML_ENTITIES).map(([k, v]) => [v, k])
    )
    
    return text.replace(/&amp;|&lt;|&gt;|&quot;|&#x27;|&#x2F;|&#x60;|&#x3D;/g, (match) => {
      return entityMap[match] || match
    })
  }

  /**
   * 清理HTML标签
   */
  static stripTags(html: string): string {
    return html.replace(/<[^>]*>/g, '')
  }

  /**
   * 安全的innerHTML设置
   */
  static safeInnerHTML(element: HTMLElement, content: string): void {
    element.textContent = content
  }

  /**
   * 验证URL安全性
   */
  static isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      // 只允许http和https协议
      return ['http:', 'https:'].includes(urlObj.protocol)
    } catch {
      return false
    }
  }

  /**
   * 清理危险的URL
   */
  static sanitizeUrl(url: string): string {
    if (!XSSProtection.isValidUrl(url)) {
      return '#'
    }
    
    // 移除javascript:等危险协议
    const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:']
    const lowerUrl = url.toLowerCase()
    
    for (const protocol of dangerousProtocols) {
      if (lowerUrl.startsWith(protocol)) {
        return '#'
      }
    }
    
    return url
  }
}

// CSRF保护
export class CSRFProtection {
  private static readonly TOKEN_KEY = 'csrf_token'
  private static readonly HEADER_NAME = 'X-CSRF-Token'

  /**
   * 生成CSRF令牌
   */
  static generateToken(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 设置CSRF令牌
   */
  static setToken(token: string): void {
    sessionStorage.setItem(CSRFProtection.TOKEN_KEY, token)
    
    // 设置meta标签
    let metaTag = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement
    if (!metaTag) {
      metaTag = document.createElement('meta')
      metaTag.name = 'csrf-token'
      document.head.appendChild(metaTag)
    }
    metaTag.content = token
  }

  /**
   * 获取CSRF令牌
   */
  static getToken(): string | null {
    return sessionStorage.getItem(CSRFProtection.TOKEN_KEY)
  }

  /**
   * 验证CSRF令牌
   */
  static validateToken(token: string): boolean {
    const storedToken = CSRFProtection.getToken()
    return storedToken !== null && storedToken === token
  }

  /**
   * 为请求添加CSRF令牌
   */
  static addTokenToRequest(config: any): any {
    const token = CSRFProtection.getToken()
    if (token) {
      config.headers = config.headers || {}
      config.headers[CSRFProtection.HEADER_NAME] = token
    }
    return config
  }

  /**
   * 初始化CSRF保护
   */
  static init(): void {
    if (!CSRFProtection.getToken()) {
      const token = CSRFProtection.generateToken()
      CSRFProtection.setToken(token)
    }
  }
}

// 输入验证
export class InputValidator {
  /**
   * 验证邮箱格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * 验证密码强度
   */
  static validatePassword(password: string): {
    isValid: boolean
    errors: string[]
    strength: 'weak' | 'medium' | 'strong'
  } {
    const errors: string[] = []
    let score = 0

    if (password.length < 8) {
      errors.push('密码长度至少8位')
    } else {
      score += 1
    }

    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母')
    } else {
      score += 1
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母')
    } else {
      score += 1
    }

    if (!/\d/.test(password)) {
      errors.push('密码必须包含数字')
    } else {
      score += 1
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含特殊字符')
    } else {
      score += 1
    }

    let strength: 'weak' | 'medium' | 'strong' = 'weak'
    if (score >= 4) strength = 'strong'
    else if (score >= 3) strength = 'medium'

    return {
      isValid: errors.length === 0,
      errors,
      strength
    }
  }

  /**
   * 验证用户名
   */
  static isValidUsername(username: string): boolean {
    // 用户名只能包含字母、数字、下划线，长度3-20位
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
    return usernameRegex.test(username)
  }

  /**
   * 验证手机号
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }

  /**
   * 验证身份证号
   */
  static isValidIdCard(idCard: string): boolean {
    const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
    return idCardRegex.test(idCard)
  }

  /**
   * 清理输入字符串
   */
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/\s+/g, ' ') // 多个空格替换为单个空格
      .replace(/[<>]/g, '') // 移除尖括号
  }

  /**
   * 验证文件类型
   */
  static isValidFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.type)
  }

  /**
   * 验证文件大小
   */
  static isValidFileSize(file: File, maxSizeInMB: number): boolean {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024
    return file.size <= maxSizeInBytes
  }
}

// 内容安全策略
export class ContentSecurityPolicy {
  /**
   * 设置CSP头部
   */
  static setCSPHeader(): void {
    const cspDirectives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://www.google.com",
      "style-src 'self' 'unsafe-inline' https://www.gstatic.com https://fonts.googleapis.com",
      "img-src 'self' data: https: blob:",
      "font-src 'self' https: data:",
      "connect-src 'self' https: wss: ws:",
      "media-src 'self' https: data:",
      "object-src 'none'",
      "frame-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "worker-src 'self' blob:"
    ]

    const cspValue = cspDirectives.join('; ')
    
    // 设置meta标签
    let metaTag = document.querySelector('meta[http-equiv="Content-Security-Policy"]') as HTMLMetaElement
    if (!metaTag) {
      metaTag = document.createElement('meta')
      metaTag.httpEquiv = 'Content-Security-Policy'
      document.head.appendChild(metaTag)
    }
    metaTag.content = cspValue
  }
}

// 安全存储
export class SecureStorage {
  private static readonly ENCRYPTION_KEY = 'secure_storage_key'

  /**
   * 简单的字符串加密（仅用于演示，生产环境应使用更强的加密）
   */
  private static encrypt(text: string): string {
    return btoa(encodeURIComponent(text))
  }

  /**
   * 简单的字符串解密
   */
  private static decrypt(encryptedText: string): string {
    try {
      return decodeURIComponent(atob(encryptedText))
    } catch {
      return ''
    }
  }

  /**
   * 安全存储数据
   */
  static setItem(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value)
      const encryptedValue = SecureStorage.encrypt(serializedValue)
      localStorage.setItem(key, encryptedValue)
    } catch (error) {
      console.error('安全存储失败:', error)
    }
  }

  /**
   * 安全获取数据
   */
  static getItem<T>(key: string): T | null {
    try {
      const encryptedValue = localStorage.getItem(key)
      if (!encryptedValue) return null
      
      const decryptedValue = SecureStorage.decrypt(encryptedValue)
      return JSON.parse(decryptedValue)
    } catch (error) {
      console.error('安全读取失败:', error)
      return null
    }
  }

  /**
   * 移除数据
   */
  static removeItem(key: string): void {
    localStorage.removeItem(key)
  }

  /**
   * 清空所有数据
   */
  static clear(): void {
    localStorage.clear()
  }
}

// 安全事件监听
export class SecurityEventListener {
  private static listeners: Map<string, Function[]> = new Map()

  /**
   * 监听安全事件
   */
  static addEventListener(event: string, callback: Function): void {
    if (!SecurityEventListener.listeners.has(event)) {
      SecurityEventListener.listeners.set(event, [])
    }
    SecurityEventListener.listeners.get(event)!.push(callback)
  }

  /**
   * 触发安全事件
   */
  static dispatchEvent(event: string, data?: any): void {
    const callbacks = SecurityEventListener.listeners.get(event)
    if (callbacks) {
      callbacks.forEach(callback => callback(data))
    }
  }

  /**
   * 移除事件监听
   */
  static removeEventListener(event: string, callback: Function): void {
    const callbacks = SecurityEventListener.listeners.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 初始化安全监听
   */
  static init(): void {
    // 监听控制台访问
    let devtools = false
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
        if (!devtools) {
          devtools = true
          SecurityEventListener.dispatchEvent('devtools-opened')
        }
      } else {
        devtools = false
      }
    }, 500)

    // 监听右键菜单
    document.addEventListener('contextmenu', (e) => {
      SecurityEventListener.dispatchEvent('context-menu', e)
    })

    // 监听键盘快捷键
    document.addEventListener('keydown', (e) => {
      if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
        SecurityEventListener.dispatchEvent('devtools-shortcut', e)
      }
    })
  }
}

// 安全管理器
export class SecurityManager {
  private static initialized = false

  /**
   * 初始化安全系统
   */
  static init(): void {
    if (SecurityManager.initialized) return

    // 初始化CSRF保护
    CSRFProtection.init()

    // 设置CSP
    ContentSecurityPolicy.setCSPHeader()

    // 初始化安全事件监听
    SecurityEventListener.init()

    // 设置安全事件处理
    SecurityEventListener.addEventListener('devtools-opened', () => {
      console.warn('检测到开发者工具被打开')
    })

    SecurityEventListener.addEventListener('context-menu', (e: Event) => {
      if (import.meta.env.PROD) {
        e.preventDefault()
        console.warn('右键菜单已被禁用')
      }
    })

    SecurityEventListener.addEventListener('devtools-shortcut', (e: KeyboardEvent) => {
      if (import.meta.env.PROD) {
        e.preventDefault()
        console.warn('开发者工具快捷键已被禁用')
      }
    })

    SecurityManager.initialized = true
    console.log('安全系统初始化完成')
  }

  /**
   * 获取安全报告
   */
  static getSecurityReport(): any {
    return {
      timestamp: new Date().toISOString(),
      csrfToken: CSRFProtection.getToken(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      referrer: document.referrer,
      cookiesEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack,
      language: navigator.language,
      platform: navigator.platform,
      onLine: navigator.onLine
    }
  }
}

// 所有安全工具类已在上方使用 export class 语法导出
// 无需重复导出
